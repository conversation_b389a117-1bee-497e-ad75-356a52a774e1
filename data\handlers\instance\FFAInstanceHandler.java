package instance;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.aionemu.commons.utils.Rnd;
import com.aionemu.gameserver.instance.handlers.GeneralInstanceHandler;
import com.aionemu.gameserver.model.EmotionType;
import com.aionemu.gameserver.model.gameobjects.Creature;
import com.aionemu.gameserver.model.gameobjects.player.Player;
import com.aionemu.gameserver.network.aion.serverpackets.SM_EMOTION;
import com.aionemu.gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import com.aionemu.gameserver.services.player.PlayerReviveService;
import com.aionemu.gameserver.services.teleport.TeleportService;
import com.aionemu.gameserver.utils.PacketSendUtility;
import com.aionemu.gameserver.utils.ThreadPoolManager;
import com.aionemu.gameserver.world.WorldMapInstance;

/**
 * Base FFA Instance Handler
 * Handles player resurrection in FFA events with proper healing, random spawn locations, and cooldown resets
 *
 * <AUTHOR> System
 */
public abstract class FFAInstanceHandler extends GeneralInstanceHandler {

    // Store spawn coordinates as simple float arrays [x, y, z, heading]
    private final List<float[]> spawnLocations = new ArrayList<>();

    // Track recently used spawn points to prevent collision (playerId -> spawn coordinates)
    private final ConcurrentMap<Integer, float[]> recentSpawns = new ConcurrentHashMap<>();

    // Cleanup interval for recent spawns (5 seconds)
    private static final long SPAWN_CLEANUP_INTERVAL = 5000;

    public FFAInstanceHandler(WorldMapInstance instance) {
        super(instance);
        initializeSpawnLocations();
    }

    /**
     * Initialize spawn locations - to be implemented by subclasses for each map
     */
    protected abstract void initializeSpawnLocations();

    /**
     * Add a spawn location to the list
     */
    protected void addSpawnLocation(float x, float y, float z, byte heading) {
        spawnLocations.add(new float[]{x, y, z, heading});
    }

    /**
     * Get a unique spawn location that's not currently occupied
     * Returns a spawn point that's different from recently used ones
     */
    private float[] getUniqueSpawnLocation(Player player) {
        if (spawnLocations.isEmpty()) {
            return new float[]{242.52405f, 424.71637f, 103.80612f, 0}; // Fallback
        }

        // Clean up old spawn records (older than 5 seconds)
        long currentTime = System.currentTimeMillis();
        recentSpawns.entrySet().removeIf(entry -> {
            float[] coords = entry.getValue();
            return coords.length > 4 && (currentTime - (long)coords[4]) > SPAWN_CLEANUP_INTERVAL;
        });

        // Try to find a spawn point that's not recently used
        List<float[]> availableSpawns = new ArrayList<>();
        for (float[] spawn : spawnLocations) {
            boolean isRecentlyUsed = false;
            for (float[] recentSpawn : recentSpawns.values()) {
                if (Math.abs(spawn[0] - recentSpawn[0]) < 5.0f &&
                    Math.abs(spawn[1] - recentSpawn[1]) < 5.0f &&
                    Math.abs(spawn[2] - recentSpawn[2]) < 5.0f) {
                    isRecentlyUsed = true;
                    break;
                }
            }
            if (!isRecentlyUsed) {
                availableSpawns.add(spawn);
            }
        }

        // If no unique spawns available, use any spawn (better than collision)
        if (availableSpawns.isEmpty()) {
            availableSpawns = spawnLocations;
        }

        // Select random spawn from available ones
        float[] selectedSpawn = Rnd.get(availableSpawns);

        // Record this spawn with timestamp
        float[] spawnWithTime = new float[]{selectedSpawn[0], selectedSpawn[1], selectedSpawn[2], selectedSpawn[3], currentTime};
        recentSpawns.put(player.getObjectId(), spawnWithTime);

        return selectedSpawn;
    }

    @Override
    public boolean onReviveEvent(Player player) {
        // Only handle resurrection for FFA players
        if (!com.aionemu.gameserver.services.FFAService.getInstance().isPlayerInEvent(player)) {
            return super.onReviveEvent(player);
        }

        try {
            // Clear any resurrection dialogs or states first
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Full resurrection with 100% HP/MP and no soul sickness
            PlayerReviveService.revive(player, 100, 100, false, 0);

            // Ensure HP/MP is actually at 100% (double-check for reliability)
            if (player.getLifeStats() != null) {
                player.getLifeStats().setCurrentHpPercent(100);
                player.getLifeStats().setCurrentMpPercent(100);
            }

            // Remove soul sickness effect if present
            player.getEffectController().removeEffect(8291);

            // Note: Cooldowns are preserved in FFA - no cooldown reset needed

            // Update stats and send resurrection message
            player.getGameStats().updateStatsAndSpeedVisually();
            PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_REBIRTH_MASSAGE_ME());

            // Remove any death-related emotions
            PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player, EmotionType.RESURRECT), true);

            // Get unique spawn location to prevent collision
            float[] uniqueSpawn = getUniqueSpawnLocation(player);

            // Teleport to unique spawn location with slight delay to prevent rollback
            ThreadPoolManager.getInstance().schedule(() -> {
                TeleportService.teleportTo(player, instance, uniqueSpawn[0], uniqueSpawn[1],
                                         uniqueSpawn[2], (byte) uniqueSpawn[3]);
            }, 100); // 100ms delay to prevent rollback issues

            // Send message to player
            PacketSendUtility.sendMessage(player,
                "Resurrected with full HP/MP at a safe location!",
                com.aionemu.gameserver.model.ChatType.BRIGHT_YELLOW_CENTER);

        } catch (Exception e) {
            // Fallback resurrection if anything fails
            PlayerReviveService.revive(player, 100, 100, false, 0);
            TeleportService.teleportTo(player, instance, 242.52405f, 424.71637f, 103.80612f, (byte) 0);
        }

        return true;
    }

    @Override
    public boolean onDie(Player player, Creature lastAttacker) {
        // Check if player is in an active FFA event
        if (com.aionemu.gameserver.services.FFAService.getInstance().isPlayerInEvent(player)) {
            // Clear target to prevent rollback issues
            if (player.getTarget() != null) {
                player.setTarget(null);
            }

            // Clear any ongoing actions to prevent desync
            if (player.getController() != null) {
                player.getController().cancelCurrentSkill(player);
            }

            // Immediately clear resurrection states to prevent dialog
            player.unsetResPosState();
            player.setPlayerResActivate(false);

            // Schedule automatic resurrection after a shorter delay
            // This allows kill tracking to complete but prevents resurrection dialog
            ThreadPoolManager.getInstance().schedule(() -> {
                if (player.isDead() && player.isOnline()) {
                    // Automatically resurrect the player
                    onReviveEvent(player);
                }
            }, 200); // Reduced to 200ms to minimize dialog appearance chance

            // Return true to prevent normal death dialog from appearing
            // This bypasses the resurrection dialog completely for FFA players
            return true;
        }

        // Continue with normal death handling for non-FFA players
        return super.onDie(player, lastAttacker);
    }
}
